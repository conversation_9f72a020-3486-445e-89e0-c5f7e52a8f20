<?php

declare(strict_types=1);

namespace tests;

require_once __DIR__ . '/../Autoloader.php';

use tools\deckanalyzer\DeckAnalyzer;
use App\Models\Deck;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\Attributes\Depends;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\Attributes\TestDox;

define('PATH_ROOT', realpath(__DIR__ . '/../../../') . '/');

/**
 * Clase de pruebas unitarias para DeckAnalyzer
 * 
 * Esta clase contiene pruebas unitarias para todos los métodos públicos
 * de la clase DeckAnalyzer, verificando su funcionamiento correcto bajo
 * diferentes escenarios.
 */
class DeckAnalyzerTest extends TestCase
{
    private array $mockDecks = [];
    private object $statCards;
    private object $constants;
    private array $pathsStrategies;

    protected function setUp(): void
    {
        // Mock del deck con 9 cartas
        $this->mockDecks = [
            "asserts" => [
                // Deck Basics
                ["Firecracker", "Ice Spirit", "Hog Rider", "Tesla", "Knight", "Earthquake", "Arrows", "Skeletons", "Tower Princess"], //normal
                ["Wall Breakers", "Mighty Miner", "Goblin Machine", "Magic Archer", "Three Musketeers", "Lumberjack", "Arrows", "Lightning", "Dagger Duchess"], //random control
                ["Skeleton Barrel", "Mighty Miner", "Goblin Demolisher", "Magic Archer", "Spear Goblins", "Lumberjack", "The Log", "Fireball", "Tower Princess"], //random ciclo
                ["Goblin Cage", "Goblin Barrel", "Goblinstein", "Goblin Machine", "Witch", "Electro Spirit", "Barbarian Barrel", "Poison", "Tower Princess"], //random push

                // Deck Randoms
                ["Golem", "Three Musketeers", "X-Bow", "Royal Giant", "Lava Hound", "Sparky", "Elixir Collector", "Mirror", "Dagger Duchess"], // anti sinergico
                ["Arrows", "Zap", "Fireball", "Poison", "Rocket", "The Log", "Lightning", "Freeze", "Royal Chef"], // anti sinergico: solo hechizos
                ["Cannon", "Tesla", "Inferno Tower", "Bomb Tower", "Goblin Cage", "Barbarian Hut", "Elixir Collector", "Mortar", "Royal Chef"], // anti sinergico: solo estructuras
                ["Lava Hound", "Balloon", "Minions", "Mega Minion", "Inferno Dragon", "Baby Dragon", "Flying Machine", "Skeleton Dragons", "Dagger Duchess"], // anti sinergico: solo aereo
                ["Ice Spirit", "Skeletons", "Electro Spirit", "Fire Spirit", "Goblins", "Spear Goblins", "Bats", "The Log", "Cannoneer"], // anti sinergico: ciclo sin win condition

                // Deck Classic
                ["Knight", "Princess", "Goblin Barrel", "Goblin Gang", "Ice Spirit", "The Log", "Rocket", "Tesla", "Tower Princess"], // log bait clásico
                ["Hog Rider", "Ice Spirit", "Skeletons", "Musketeer", "Cannon", "Fireball", "The Log", "Ice Golem", "Tower Princess"], // ciclo rápido de montapuercos 2.6 clasico
                ["Firecracker", "Ice Spirit", "Hog Rider", "Spirit Empress", "Knight", "Earthquake", "Arrows", "Skeletons", "Tower Princess"], // normal con una carta nueva "Spirit Empress"
                ["P.E.K.K.A", "Battle Ram", "Electro Wizard", "Zap", "Poison", "Dark Prince", "Minions", "Royal Ghost", "Tower Princess"], // PEKKA Bridge Spam
                ["Giant", "Prince", "Mega Minion", "Zap", "Poison", "Elixir Collector", "Minions", "Electro Wizard", "Tower Princess"], // Giant Double Prince
                ["Lava Hound", "Balloon", "Mega Minion", "Minions", "Arrows", "Zap", "Tombstone", "Inferno Dragon", "Tower Princess"], // LavaLoon
                ["Graveyard", "Poison", "Knight", "Baby Dragon", "Tornado", "Electro Wizard", "Barbarian Barrel", "Ice Wizard", "Tower Princess"], // Graveyard Poison
                ["X-Bow", "Tesla", "Ice Spirit", "Cannon", "Fireball", "Rocket", "Skeletons", "Archers", "Tower Princess"], // X-Bow Cycle
                ["Royal Giant", "Lightning", "Electro Wizard", "Ice Spirit", "Cannon", "Fireball", "Skeletons", "Barbarian Barrel", "Tower Princess"], // Royal Giant Cycle
                ["Goblin Drill", "Inferno Tower", "Ice Spirit", "Skeletons", "Fireball", "Zap", "Barbarian Barrel", "Electro Spirit", "Tower Princess"], // Goblin Drill Cycle
            ],
            "exceptions" => [
                ["Firecracker", "Ice Spirit", "Hog Rider"], //menos de 9 cartas
                ["Firecracker", "Ice Spirit", "Hog Rider", "Tesla", "Knight", "Earthquake", "Arrows", "Skeletons", "Tower Princess", "Goblin Barrel"], //más de 9 cartas
                ["Firecracker", "Ice Spirit", "Hog Rider", "Tesla", "Knight", "Earthquake", "Arrows", "Skeletons", "Tower Princess", "Goblin Barrel", "Goblin Barrel"], //cartas repetidas
                ["", "", "", "", "", "", "", "", ""], //cartas vacías
                [null, null, null, null, null, null, null, null, null], //cartas nulas
                [false, false, false, false, false, false, false, false, false], //cartas falsas
                [true, true, true, true, true, true, true, true, true], //cartas verdaderas
                ["InvalidCard", "Ice Spirit", "Hog Rider", "Tesla", "Knight", "Earthquake", "Arrows", "Skeletons", "Tower Princess"], // carta inválida
                ["Firecracker", "Ice Spirit", "Hog Rider", "Tesla", "Knight", "Earthquake", "Arrows", "Skeletons", "InvalidCard"], // otra carta inválida
                ["Firecracker", "Ice Spirit", "Hog Rider", "Tesla", "Knight", "Earthquake", "Arrows", "Skeletons", "Tower Princess", "InvalidCard"], // más de 9 cartas con una inválida
                [] //sin cartas
            ],
        ];

        $this->statCards = json_decode(file_get_contents(PATH_ROOT . 'App/Data/cards/statCards.json'));
        $this->constants = json_decode(file_get_contents(PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Constants.json'));
        $this->pathsStrategies = [
            "General" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/General.json',
            "Defensa" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Defense.json',
            "Adaptavilidad" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Adaptability.json',
            "Agresivo" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Aggressive.json',
            "Bait" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Bait.json',
            "Ciclo" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Cycle.json',
            "Precion" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Pressure.json',
            "Push" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/Push.json',
            "SplitPush" => PATH_ROOT . 'App/Data/tools/DeckAnalyzer/Strategies/SplitPush.json'
        ];
    }

    #[Test]
    #[TestDox('Prueba los casos válidos del constructor')]
    public function testConstructorValid(): array
    {
        $deckAnalyzers = [];
        foreach ($this->mockDecks['asserts'] as $deckNames) {
            foreach (['basic', 'intermediate', 'advanced'] as $type) {
                $Deck = new Deck($deckNames);
                try {
                    $deckAnalyzer = new DeckAnalyzer(
                        $this->statCards,
                        $this->constants,
                        $this->pathsStrategies,
                        $Deck,
                        $type
                    );
                    $this->assertInstanceOf(DeckAnalyzer::class, $deckAnalyzer);
                    $deckAnalyzers[] = $deckAnalyzer;
                } catch (\Exception $e) {
                    $this->fail("Unexpected exception: " . $e->getMessage());
                }
            }
        }
        return $deckAnalyzers;
    }

    #[Test]
    #[TestDox('Prueba las excepciones del constructor')]
    public function testConstructorInvalid(): void
    {
        foreach ($this->mockDecks['exceptions'] as $deckNames) {
            foreach (['basic', 'intermediate', 'advanced'] as $type) {
                $this->expectException(\Exception::class);
                $Deck = new Deck($deckNames);
                new DeckAnalyzer(
                    $this->statCards,
                    $this->constants,
                    $this->pathsStrategies,
                    $Deck,
                    $type
                );
            }
        }
    }

    #[Test]
    #[TestDox('Prueba los resultados de la funcion getScoreSynergy')]
    #[Depends('testConstructorValid')]
    public function getScoreSynergyTest($deckAnalyzers): void
    {
        foreach ($deckAnalyzers as $deckAnalyzer) {
            $result = $deckAnalyzer->getScoreSynergy();

            // Assert that *only* the expected keys exist
            $expectedKeys = [
                'totalScore',
                'totalPointsSynergyCards',
                'totalPointsSynergyStrategy',
                'arrayTotalPointsSynergyStrategy',
                'arrayTotalPointsSynergyCards',
                'maximumPossiblePointsStrategy',
                'maximumPossiblePointsCards',
                'arraySynergyCards',
                'arraySynergyStrategy',
                'msg',
                'topPositiveSynergies',
                'topNegativeSynergies',
                'description',
                'displayScore',
                'displayMessage',
                'displayClass',
                'formattedMessages'
            ];
            $this->assertEqualsCanonicalizing($expectedKeys, array_keys($result), "The result array should contain only the expected keys.");

            // Validate type of each key
            $this->assertIsFloat($result['totalScore']);
            $this->assertIsFloat($result['totalPointsSynergyCards']);
            $this->assertIsFloat($result['totalPointsSynergyStrategy']);
            $this->assertIsArray($result['arrayTotalPointsSynergyStrategy']);
            $this->assertIsArray($result['arrayTotalPointsSynergyCards']);
            $this->assertIsFloat($result['maximumPossiblePointsStrategy']);
            $this->assertIsFloat($result['maximumPossiblePointsCards']);
            $this->assertIsArray($result['arraySynergyCards']);
            $this->assertIsArray($result['arraySynergyStrategy']);
            $this->assertIsArray($result['msg']);
            $this->assertIsArray($result['topPositiveSynergies']);
            $this->assertIsArray($result['topNegativeSynergies']);
            $this->assertIsString($result['description']);
            $this->assertIsFloat($result['displayScore']);
            $this->assertIsString($result['displayMessage']);
            $this->assertIsString($result['displayClass']);
            $this->assertIsString($result['formattedMessages']);

            // Validate structure of nested arrays
            foreach ($result['topPositiveSynergies'] as $synergy) {
                $this->assertArrayHasKey('context', $synergy);
                $this->assertArrayHasKey('score', $synergy);
                $this->assertIsString($synergy['context']);
                $this->assertIsFloat($synergy['score']);
            }

            foreach ($result['topNegativeSynergies'] as $synergy) {
                $this->assertArrayHasKey('context', $synergy);
                $this->assertArrayHasKey('score', $synergy);
                $this->assertIsString($synergy['context']);
                $this->assertIsFloat($synergy['score']);
            }

            foreach ($result['arraySynergyCards'] as $cardSynergy) {
                $this->assertArrayHasKey('name', $cardSynergy);
                $this->assertArrayHasKey('medium', $cardSynergy);
                $this->assertArrayHasKey('pointsSynergy', $cardSynergy);
                $this->assertArrayHasKey('msg', $cardSynergy);
                $this->assertArrayHasKey('reasons', $cardSynergy);
                $this->assertArrayHasKey('SynergyCards', $cardSynergy);

                foreach ($cardSynergy['SynergyCards'] as $synergyCard) {
                    $this->assertArrayHasKey('medium', $synergyCard);
                    $this->assertArrayHasKey('pointsSynergy', $synergyCard);
                    $this->assertArrayHasKey('msg', $synergyCard);
                    $this->assertArrayHasKey('reasons', $synergyCard);

                    foreach ($synergyCard['reasons'] as $reason) {
                        $this->assertArrayHasKey('reason', $reason);
                        $this->assertArrayHasKey('points', $reason);
                        $this->assertIsString($reason['reason']);
                        $this->assertIsNumeric($reason['points']);
                    }
                }
            }

            foreach ($result['arraySynergyStrategy'] as $strategySynergy) {
                $this->assertArrayHasKey('name', $strategySynergy);
                $this->assertArrayHasKey('medium', $strategySynergy);
                $this->assertArrayHasKey('pointsSynergy', $strategySynergy);
                $this->assertArrayHasKey('msg', $strategySynergy);
                $this->assertArrayHasKey('reasons', $strategySynergy);

                foreach ($strategySynergy['reasons'] as $reason) {
                    $this->assertArrayHasKey('reason', $reason);
                    $this->assertArrayHasKey('points', $reason);
                    $this->assertIsString($reason['reason']);
                    $this->assertIsNumeric($reason['points']);
                }
            }

            foreach ($result['arrayTotalPointsSynergyCards'] as $points) {
                $this->assertIsFloat($points);
            }

            foreach ($result['arrayTotalPointsSynergyStrategy'] as $points) {
                $this->assertIsFloat($points);
            }

            foreach ($result['msg'] as $message) {
                $this->assertIsString($message);
            }

            // Validate total score range
            $this->assertGreaterThanOrEqual(0, $result['totalScore']);
            $this->assertLessThanOrEqual(100, $result['totalScore']);
        }
    }
}
