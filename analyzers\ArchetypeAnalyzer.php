<?php

declare(strict_types=1);

namespace tools\deckanalyzer\analyzers;

use tools\deckanalyzer\AnalyzerInterface;

/**
 * Analizador de Arquetipos - Identifica el arquetipo del mazo basado en definiciones predefinidas.
 *
 * Este analizador se encarga de:
 * - Identificar el arquetipo principal del mazo
 * - Comparar cartas clave y alternativas
 * - Evaluar rangos de elixir y ciclo
 * - Detectar cartas incompatibles
 */
class ArchetypeAnalyzer implements AnalyzerInterface
{
    /** @var object El mazo que se está analizando */
    private object $deck;

    /** @var callable Función para obtener el costo promedio de elixir */
    private $getAverageElixirCostCallback;

    /** @var callable Función para obtener el ciclo corto */
    private $getShortCycleCallback;

    public function __construct(callable $getAverageElixirCostCallback, callable $getShortCycleCallback)
    {
        $this->getAverageElixirCostCallback = $getAverageElixirCostCallback;
        $this->getShortCycleCallback = $getShortCycleCallback;
    }

    public function analyze(object $deck): array
    {
        $this->deck = $deck;
        return $this->identifyArchetype();
    }

    /**
     * Identifica el arquetipo del mazo actual basado en definiciones predefinidas.
     *
     * Carga las definiciones de arquetipos desde un archivo JSON y compara las cartas del mazo,
     * el coste promedio de elixir y el coste del ciclo corto con las condiciones de cada arquetipo.
     *
     * @return array Un array asociativo con:
     *               - 'archetype': El nombre del arquetipo principal ('Log Bait', 'Híbrido', 'Desconocido').
     *               - 'details': Un array con información adicional (ej. nombres de arquetipos coincidentes si es 'Híbrido').
     * @throws \Exception Si el archivo de arquetipos no existe o no se puede decodificar.
     */
    public function identifyArchetype(): array
    {
        $archetypesPath = __DIR__ . '/../../../App/Data/tools/DeckAnalyzer/archetypes.json';
        $description = 'El arquetipo identifica el estilo de juego principal de tu mazo. Se determina comparando la composición de tu mazo (cartas clave, coste promedio, coste de ciclo) con patrones conocidos (ej. Log Bait, Lavaloon, Pekka Bridge Spam). Verificamos: 1) Presencia de cartas esenciales para el arquetipo. 2) Coste de elixir promedio y de ciclo dentro de los rangos típicos. 3) Ausencia de cartas que contradigan el estilo del arquetipo. Conocer tu arquetipo te ayuda a entender sus fortalezas, debilidades y plan de juego general.';

        if (!file_exists($archetypesPath)) {
            throw new \Exception("Archetypes file not found: {$archetypesPath}");
        }

        $archetypesContent = file_get_contents($archetypesPath);
        if ($archetypesContent === false) {
            throw new \Exception("Could not read archetypes file: {$archetypesPath}");
        }

        $archetypes = json_decode($archetypesContent, true);
        if ($archetypes === null) {
            throw new \Exception("Could not decode archetypes JSON: " . json_last_error_msg());
        }

        $deckCards = array_map(fn($card) => $card->name, array_merge($this->deck->Cards, $this->deck->CardsEvo));
        $averageElixir = call_user_func($this->getAverageElixirCostCallback);
        $shortCycleCost = call_user_func($this->getShortCycleCallback);
        $matchedArchetypes = [];

        foreach ($archetypes as $archetypeName => $definition) {
            // Check for key cards: ALL key cards must be present
            if (!empty($definition['keyCards']) && array_diff($definition['keyCards'], $deckCards)) {
                continue; // Missing a key card
            }

            // Check for alternative key cards: AT LEAST ONE set of alternatives must be present
            $alternativeMatch = true; // Assume true if no alternatives defined
            if (!empty($definition['alternativeKeyCards'])) {
                $alternativeMatch = false; // Reset to false, needs at least one match
                foreach ($definition['alternativeKeyCards'] as $alternativeSet) {
                    if (empty(array_diff($alternativeSet, $deckCards))) {
                        $alternativeMatch = true; // Found a matching set
                        break;
                    }
                }
            }
            if (!$alternativeMatch) {
                continue; // Did not meet alternative key card requirement
            }

            // Check elixir range
            if (
                $averageElixir < ($definition['elixirRange']['min'] ?? 0) ||
                $averageElixir > ($definition['elixirRange']['max'] ?? 10)
            ) {
                continue;
            }

            // Check cycle cost range
            if (
                $shortCycleCost < ($definition['cycleCostRange']['min'] ?? 0) ||
                $shortCycleCost > ($definition['cycleCostRange']['max'] ?? 20)
            ) {
                continue;
            }

            // Check for incompatible cards
            if (!empty($definition['incompatibleCards']) && array_intersect($definition['incompatibleCards'], $deckCards)) {
                continue; // Contains an incompatible card
            }

            $matchedArchetypes[] = $archetypeName;
        }

        // Determine the final result
        $matchCount = count($matchedArchetypes);
        if ($matchCount === 1) {
            return ['archetype' => $matchedArchetypes[0], 'details' => [], 'description' => $description];
        } elseif ($matchCount > 1) {
            // If multiple archetypes match, prioritize based on key card count? Or just call it Hybrid?
            // For now, keep it as Hybrid. Could add prioritization later.
            return ['archetype' => 'Híbrido', 'details' => $matchedArchetypes, 'description' => $description];
        } else {
            // Could add logic here to identify characteristics if 'Desconocido'
            return ['archetype' => 'Desconocido', 'details' => [], 'description' => $description];
        }
    }
}
