name: Deploy DeckAnalyzer via FTP

on:
  # Deploy after successful release
  workflow_run:
    workflows: ["Release"]
    types:
      - completed
    branches:
      - main
  # Allow manual deployment
  workflow_dispatch:

jobs:
  ftp-deploy:
    runs-on: ubuntu-latest
    # Only deploy if the release workflow succeeded or if manually triggered
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}

    steps:
      - name: Checkout del código
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Solo trae el último commit, evita traer .git/

      - name: Verificar archivos requeridos
        run: |
          echo "Verificando archivos críticos..."
          if [ ! -f "DeckAnalyzer.php" ]; then
            echo "❌ Error: DeckAnalyzer.php no encontrado"
            exit 1
          fi
          echo "✅ Todos los archivos críticos están presentes"

      - name: Mostrar información de despliegue
        run: |
          echo "🚀 Iniciando despliegue..."
          echo "Commit: ${{ github.sha }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Workflow: ${{ github.workflow }}"

      - name: <PERSON>ple<PERSON> por FTP
        uses: SamKirkland/FTP-Deploy-Action@v4.3.5
        with:
          server: ${{ secrets.FTP_SERVER }}
          username: ${{ secrets.FTP_USERNAME }}
          password: ${{ secrets.FTP_PASSWORD }}
          server-dir: /public_html/api/tools/deckanalyzer/
          local-dir: ./
          exclude: |
            .git/
            **/.git/**
            **/.gitignore
            **/.github/**
            **/node_modules/**
            **/tests/**
            **/.env*
            **/.releaserc*
            **/package-lock.json
            **/package.json
            **/.vscode/**
            **/.clinerules/**
            **/README*.md
            **/CHANGELOG.md
            **/*.log
            **/.DS_Store
            **/Thumbs.db
            **/.npmrc
            **/.editorconfig
            **/composer.json
            **/composer.lock
            **/vendor/**

      - name: Verificar despliegue
        run: |
          echo "✅ Despliegue completado exitosamente"
