<?php

declare(strict_types=1);

namespace tools\deckanalyzer;

use tools\deckanalyzer\analyzers\SynergyAnalyzer;
use tools\deckanalyzer\analyzers\VersatilityAnalyzer;
use tools\deckanalyzer\analyzers\ArchetypeAnalyzer;
use tools\deckanalyzer\analyzers\BattleRoleAnalyzer;
use tools\deckanalyzer\analyzers\WeaknessAnalyzer;

/**
 * Clase DeckAnalyzer - Analiza mazos de Clash Royale para evaluar su efectividad y estrategias.
 *
 * Esta clase actúa como fachada coordinando diferentes tipos de análisis especializados.
 * Cada tipo de análisis está separado en su propio analizador para mejor organización y mantenibilidad.
 */
class DeckAnalyzer
{
    /** @var object El mazo que se está analizando */
    public object $Deck;

    /** @var object Constantes utilizadas para los cálculos de análisis */
    private object $constants;

    /** @var array Estrategias disponibles para el análisis */
    private array $strategies;

    /** @var string Tipo de análisis a realizar ('basic', 'intermediate', 'advanced') */
    public string $type;

    /** @var array Rutas de los archivos de estrategias */
    public array $pathsStrategies;

    /** @var array Todas las cartas con estadísticas de nivel 11 */
    public array $allCardsInstanceLevel11;

    /**
     * Constructor de la clase DeckAnalyzer.
     *
     * @param object $statCards Objeto con estadísticas de todas las cartas.
     * @param object $constants Objeto con constantes utilizadas para los cálculos.
     * @param array $pathsStrategies Array con las rutas de los archivos de estrategias.
     * @param object $instanceDeck Objeto representando el mazo a analizar.
     * @param string $type Tipo de análisis a realizar ('basic', 'intermediate', 'advanced').
     * @throws \Exception Si el tipo de análisis no es válido o si el mazo no cumple con los requisitos.
     */
    public function __construct(object $statCards, object $constants, array $pathsStrategies, object $instanceDeck, string $type)
    {
        $allowedTypes = ['basic', 'intermediate', 'advanced'];
        if (!in_array($type, $allowedTypes))
            throw new \Exception("Invalid type: {$type}. Allowed types are: " . implode(', ', $allowedTypes));

        if (!isset($instanceDeck->Cards) || !is_array($instanceDeck->Cards))
            throw new \Exception("Invalid Deck instance: 'Cards' property is missing or not an array.");

        if (!isset($instanceDeck->CardsEvo) || !is_array($instanceDeck->CardsEvo))
            throw new \Exception("Invalid Deck instance: 'CardsEvo' property is missing or not an array.");

        $this->constants = $constants;
        $this->pathsStrategies = $pathsStrategies;
        $this->Deck = $instanceDeck;
        $this->type = $type;

        foreach ($pathsStrategies as $keyPath => $path) {
            if (!file_exists($path)) {
                throw new \Exception("El archivo de estrategia no existe: {$path}");
            }

            $pathData = file_get_contents($path);
            if ($pathData === false) {
                throw new \Exception("No se pudo leer el archivo de estrategia: {$path}");
            }

            $this->strategies[$keyPath] = json_decode(json: $pathData);
            if ($this->strategies[$keyPath] === null) {
                throw new \Exception("Error al decodificar el JSON del archivo de estrategia: " . json_last_error_msg());
            }
        }

        foreach (array_merge($statCards->cards, $statCards->towerCards) as $card) {
            // Ensure stats are at level 11 for consistent analysis
            $card->damage = $card->damage->level11 ?? 0;
            $card->dps = $card->dps->level11 ?? 0;
            $card->hitpoints = $card->hitpoints->level11 ?? 0;
            // Handle potential missing tower damage
            if (isset($card->TowerDamage)) {
                $card->towerDamage = $card->TowerDamage->level11 ?? 0;
            }
            $this->allCardsInstanceLevel11[] = $card;
        }
    }

    private function formatAverages(array $averages): array
    {
        $thresholdsPath = ($this->type == 'basic')
            ? __DIR__ . '/data/thresholds_basic.json'
            : __DIR__ . '/data/thresholds.json';
        $thresholdsData = json_decode(file_get_contents($thresholdsPath), true);

        foreach ($averages as $label => &$section) {
            // Ensure totalScore exists, default to 0 if not (might happen in edge cases)
            $score = $section['totalScore'] ?? 0;
            $section['displayScore'] = $score; // Keep original score
            $section['displayMessage'] = 'N/A'; // Default message
            $section['displayClass'] = ''; // Default CSS class

            if (isset($thresholdsData['thresholds'][$label])) {
                // Sort thresholds descending to find the correct one
                krsort($thresholdsData['thresholds'][$label]);
                foreach ($thresholdsData['thresholds'][$label] as $threshold => $message) {
                    if ($score >= $threshold) {
                        $section['displayMessage'] = $message;
                        // Find the correct CSS class
                        krsort($thresholdsData['levelClasses']); // Sort classes descending
                        foreach ($thresholdsData['levelClasses'] as $classThreshold => $class) {
                            if ($score >= $classThreshold) {
                                $section['displayClass'] = $class;
                                break; // Exit class loop once found
                            }
                        }
                        break; // Exit threshold loop once found
                    }
                }
            }
            // Process additional messages if they exist
            if (!empty($section['msg']) && is_array($section['msg'])) {
                $section['formattedMessages'] = implode('<br><br>', array_map('htmlspecialchars', $section['msg']));
            } else {
                $section['formattedMessages'] = '';
            }
        }
        unset($section); // Break reference
        return $averages[array_keys($averages)[0]];
    }

    /**
     * Analiza los promedios de sinergia del mazo, considerando sinergias de cartas y estrategias.
     * Delegado al SynergyAnalyzer especializado.
     *
     * @return array Retorna un array con los resultados del análisis de sinergias.
     * @throws \Exception Si ocurre un error al buscar sinergias o decodificar datos.
     */
    public function getScoreSynergy(): array
    {
        $synergyAnalyzer = new SynergyAnalyzer(
            $this->constants,
            $this->strategies,
            $this->allCardsInstanceLevel11
        );

        $resSynergy = $synergyAnalyzer->analyze($this->Deck);
        return $this->formatAverages(["Sinergia" => $resSynergy]);
    }

    /**
     * Analiza la versatilidad del mazo evaluando la cobertura de diferentes tipos de ataque.
     * Delegado al VersatilityAnalyzer especializado.
     *
     * @return array Retorna un array con los resultados del análisis de versatilidad.
     * @throws \Exception Si ocurre un error al determinar el grupo de una carta.
     */
    public function getScoreVersatility(): array
    {
        $versatilityAnalyzer = new VersatilityAnalyzer(
            $this->constants
        );

        $resVersatility = $versatilityAnalyzer->analyze($this->Deck);
        return $this->formatAverages(["Versatilidad" => $resVersatility]);
    }

    /**
     * Calcula los promedios de ataque o defensa del mazo.
     * Delegado al BattleRoleAnalyzer especializado.
     *
     * @param string $type El tipo de promedio a calcular: 'attack' para ataque, 'defense' para defensa.
     * @return array Un array asociativo con los resultados del análisis de roles de batalla.
     * @throws \InvalidArgumentException Si el `$type` proporcionado no es 'attack' o 'defense'.
     * @throws \Exception Si ocurre un error al calcular los promedios o roles de batalla.
     */
    public function getBattleRolesScores(string $type): array
    {
        $battleRoleAnalyzer = new BattleRoleAnalyzer(
            $this->constants,
            $this->allCardsInstanceLevel11,
            $type
        );

        $typesRoles = [
            'attack' => 'Ataque',
            'defense' => 'Defensa'
        ];

        $resVersatility = $battleRoleAnalyzer->analyze($this->Deck);
        return $this->formatAverages([$typesRoles[$type] => $resVersatility]);
    }

    /**
     * Calcula los promedios de ataque y defensa por grupos de cartas
     * Calcula los porcentajes de contribución al ataque y defensa por grupos de cartas (win condition, terrestre, aéreo, defensivo/win, hechizo).
     *
     * Utiliza los grupos de cartas obtenidos del análisis de versatilidad y calcula qué porcentaje
     * del puntaje total de ataque y defensa aporta cada grupo.
     *
     * @param array $versatilityGroups Array asociativo con las cartas agrupadas por tipo (resultado de `getScoreVersatility`).
     *                                 Ej: ['cardwin' => [...], 'cardter' => [...], ... 'cardhech' => ['hech1' => [...], ...]]
     * @return array Un array asociativo donde las claves son los tipos de grupo ('cardwin', 'cardter', etc.)
     *               y los valores son arrays con los porcentajes de 'ataque' y 'defensa' como strings (ej. '25%').
     * @throws \Exception Si ocurre un error al calcular los promedios de los grupos o al acceder a constantes.
     */
    public function getBattleRolesScoresByGroup(array $versatilityGroups): array
    {
        $battleRoleAnalyzer = new BattleRoleAnalyzer(
            $this->constants,
            $this->allCardsInstanceLevel11,
            "null"
        );

        return $battleRoleAnalyzer->getBattleRolesScoresByGroup($versatilityGroups);
    }

    /**
     * Calcula el costo total de elixir del ciclo corto del mazo.
     *
     * Ordena las cartas del mazo por costo de elixir de manera ascendente
     * y luego suma el costo de elixir de las cuatro cartas más baratas.
     * @return int El costo total de elixir de las 4 cartas más baratas (excluyendo cartas de tipo 'Tower').
     */
    public function getShortCycle(): int
    {
        // Include evo cards in cycle calculation
        $cards = array_filter(array_merge($this->Deck->Cards, $this->Deck->CardsEvo), function ($card) {
            return $card->type != 'tower';
        });

        usort($cards, function ($a, $b) {
            return $a->elixirCost <=> $b->elixirCost;
        });

        $shortCycle = array_slice($cards, 0, 4);
        $totalElixir = array_sum(array_column($shortCycle, 'elixirCost'));

        return $totalElixir;
    }

    /**
     * Calcula el promedio de coste de elixir del mazo, excluyendo torres.
     * @return float El costo promedio de elixir del mazo (excluyendo cartas de tipo 'Tower'), redondeado a un decimal.
     *               Retorna 0.0 si no hay cartas válidas para calcular el promedio (división por cero).
     */
    public function getAverageElixirCost(): float
    {
        // Include evo cards in calculation
        $cards = array_filter(array_merge($this->Deck->Cards, $this->Deck->CardsEvo), function ($card) {
            return $card->type != 'tower';
        });

        $totalElixir = array_sum(array_column($cards, 'elixirCost'));
        $numCards = count($cards);

        // Avoid division by zero
        return $numCards > 0 ? round($totalElixir / $numCards, 1) : 0.0;
    }

    /**
     * Calcula el DPS de una carta
     * @param object $card Carta a analizar
     * @param array $param Parámetros adicionales de análisis
     * @return float DPS calculado
     */
    private function calculateDPS(object $card): int|float
    {
        $dps = 0;
        $special = $card->special ? array_keys(json_decode(json_encode($card->special), true)) : [];
        $differentUnits = isset($card->special->differentUnits) ? (array) $card->special->differentUnits : [];

        if (!empty($differentUnits)) {
            $dpsValues = array_map(function ($unitCount, $unitName) use ($card) {
                // Need a way to get card stats by name here. Assuming a helper or Card constructor logic.
                // This part might need adjustment based on how Card model fetches data.
                try {
                    $unitCard = array_filter(
                        $this->allCardsInstanceLevel11,
                        fn($card) => $card->name == $unitName
                    )[0] ?? null;
                    if (!$unitCard) {
                        throw new \Exception("Unit card not found for name: " . $unitName);
                    }
                    return ($unitCard->dps ?? 0) * $unitCount;
                } catch (\Exception $e) {
                    // Handle cases where the unit name doesn't correspond to a known card
                    error_log("Could not find card data for unit: " . $unitName . " in calculateDPS");
                    return 0;
                }
            }, $differentUnits, array_keys($differentUnits));
            $dps = array_sum($dpsValues);
        } elseif (in_array('dpsIncrement', $special)) {
            // DPS increment logic might be complex, using base DPS as a starting point
            $dps = ($card->dps ?? 0) * ($card->units ?? 1);
        } else {
            // Standard calculation
            $dps = ($card->dps ?? 0) * ($card->units ?? 1);
        }
        return $dps;
    }

    /**
     * Analiza el mazo para determinar la cobertura de diferentes tipos de defensa.
     *
     * Evalúa las cartas del mazo para identificar si proporcionan defensa contra
     * amenazas aéreas, tanques, enjambres, etc., así como capacidades de control
     * y daño directo mediante hechizos.
     *
     * @return array Un array asociativo donde las claves son los tipos de defensa
     *               (ej. 'anti_air', 'anti_tank') y los valores son booleanos
     *               indicando si el mazo cubre ese tipo de defensa.
     * @throws \Exception Si ocurre un error al acceder a las propiedades de las cartas o decodificar JSON.
     */
    public function getDefenseTypesCoverage(): array
    {
        $defenseCoverage = [
            'anti_air' => ['isCovered' => false, 'cards' => []],
            'anti_tank' => ['isCovered' => false, 'cards' => []], // Alto daño único o incremental
            'splash_ground' => ['isCovered' => false, 'cards' => []], // Daño de área terrestre
            'splash_air' => ['isCovered' => false, 'cards' => []], // Daño de área aéreo
            'swarm_defense' => ['isCovered' => false, 'cards' => []], // Defensa contra enjambres (multi-unidad, splash, rápido)
            'building_defense' => ['isCovered' => false, 'cards' => []], // Estructuras defensivas
            'direct_damage_spell' => ['isCovered' => false, 'cards' => []], // Hechizos de daño directo a torre
            'control' => ['isCovered' => false, 'cards' => []], // Aturdir, ralentizar, empujar, congelar, etc.
        ];

        // Umbral de DPS para considerar anti-tanque (ajustable)
        $antiTankDpsThreshold = $this->constants->ANTI_TANK_DPS_THRESHOLD ?? 300; // Example, adjust as needed
        $antiTankDamageThreshold = $this->constants->ANTI_TANK_DAMAGE_THRESHOLD ?? 500; // Example for high single hit

        // Atributos de control
        $controlAttributes = $this->constants->CONTROL_ATTRIBUTES_LIST ?? ['stun', 'slow', 'knockback', 'freeze', 'pull', 'charge', 'dash', 'jump']; // Include charge/dash/jump which can interrupt/distract

        foreach (array_merge($this->Deck->Cards, $this->Deck->CardsEvo) as $card) {
            $specialKeys = $card->special ? array_keys(json_decode(json_encode($card->special), true)) : [];
            $specialEvoKeys = ($card->evolution && $card->specialEvo) ? array_keys(json_decode(json_encode($card->specialEvo), true)) : [];
            $allSpecialKeys = array_unique(array_merge($specialKeys, $specialEvoKeys));
            $cardName = $card->name; // For adding to the list

            // Anti-Aéreo
            if (in_array('aer', $card->Attack)) {
                $defenseCoverage['anti_air']['isCovered'] = true;
                $defenseCoverage['anti_air']['cards'][] = $cardName;
            }

            // Anti-Tanque (Mejorado)
            $isAntiTank = false;
            if ($card->type != 'spell') {
                if (
                    ($card->TypeAttack == 'unique' && ($card->dps ?? 0) >= $antiTankDpsThreshold) || // Alto DPS único
                    (($card->damage ?? 0) >= $antiTankDamageThreshold) || // Alto daño por golpe
                    (in_array('dpsIncrement', $allSpecialKeys)) || // Daño incremental
                    ($card->units > 3 && ($card->dps ?? 0) * $card->units >= $antiTankDpsThreshold * 1.5) // Swarm with high total DPS (e.g., Skarmy, Goblins)
                ) {
                    $isAntiTank = true;
                }
            } elseif ($cardName == 'Rocket' || $cardName == 'Lightning') { // Hechizos de alto daño
                $isAntiTank = true;
            }
            if ($isAntiTank) {
                $defenseCoverage['anti_tank']['isCovered'] = true;
                $defenseCoverage['anti_tank']['cards'][] = $cardName;
            }

            // Salpicadura Terrestre
            if ($card->TypeAttack == 'splash' && in_array('ter', $card->Attack)) {
                $defenseCoverage['splash_ground']['isCovered'] = true;
                $defenseCoverage['splash_ground']['cards'][] = $cardName;
            }

            // Salpicadura Aérea
            if ($card->TypeAttack == 'splash' && in_array('aer', $card->Attack)) {
                $defenseCoverage['splash_air']['isCovered'] = true;
                $defenseCoverage['splash_air']['cards'][] = $cardName;
            }

            // Defensa contra Enjambres
            $isSwarmDefense = false;
            if ($card->type != 'spell') {
                if (
                    ($card->units > 1 && ($card->hitspeed ?? 2) < 1.5) || // Multi-unidad rápida
                    ($card->TypeAttack == 'splash') || // Salpicadura
                    (($card->hitspeed ?? 2) < 0.8) || // Ataque muy rápido
                    in_array('GenerationUnits', $allSpecialKeys) // Genera unidades
                ) {
                    $isSwarmDefense = true;
                }
            } elseif (in_array($cardName, ['Arrows', 'Zap', 'The Log', 'Barbarian Barrel', 'Snowball', 'Tornado', 'Poison'])) { // Spells effective vs swarm
                $isSwarmDefense = true;
            }
            if ($isSwarmDefense) {
                $defenseCoverage['swarm_defense']['isCovered'] = true;
                $defenseCoverage['swarm_defense']['cards'][] = $cardName;
            }

            // Estructura Defensiva
            $isBuildingDefense = false;
            if ($card->type == 'building') {
                if ($card->territory == 'Restricted' || in_array('GenerationUnits', $allSpecialKeys)) {
                    $isBuildingDefense = true;
                }
            }
            if ($isBuildingDefense) {
                $defenseCoverage['building_defense']['isCovered'] = true;
                $defenseCoverage['building_defense']['cards'][] = $cardName;
            }

            // Hechizo de Daño Directo (a Torres)
            if ($card->type == 'spell' && property_exists($card, 'towerDamage') && $card->towerDamage > 0) {
                $defenseCoverage['direct_damage_spell']['isCovered'] = true;
                $defenseCoverage['direct_damage_spell']['cards'][] = $cardName;
            }

            // Control
            $hasControl = false;
            foreach ($controlAttributes as $controlAttr) {
                if (in_array($controlAttr, $allSpecialKeys)) {
                    $hasControl = true;
                    break;
                }
            }
            // Specific control spells
            if ($card->type == 'spell' && in_array($cardName, ['Tornado', 'The Log', 'Barbarian Barrel', 'Snowball', 'Zap', 'Freeze', 'Ice Spirit', 'Electro Spirit'])) { // Added spirits, Zap
                $hasControl = true;
            }
            if ($hasControl) {
                $defenseCoverage['control']['isCovered'] = true;
                $defenseCoverage['control']['cards'][] = $cardName;
            }

        }

        // Remove duplicates from card lists
        foreach ($defenseCoverage as &$coverageInfo) {
            if (isset($coverageInfo['cards'])) {
                $coverageInfo['cards'] = array_values(array_unique($coverageInfo['cards']));
            }
        }
        unset($coverageInfo);


        return [
            "description" => "La Cobertura Defensiva analiza si tu mazo posee respuestas adecuadas contra diferentes tipos de amenazas comunes. Se evalúa la presencia de cartas capaces de contrarrestar eficazmente cada categoría (ej. daño de área para enjambres, unidades aéreas para tanques aéreos). Un 'Sí' indica que tienes al menos una respuesta viable, mientras que un 'No' sugiere una posible vulnerabilidad.",
            "data" => $this->formatDefenseCoverage($defenseCoverage)
        ];
    }

    private function formatDefenseCoverage(array $defenseCoverage): array
    {
        $formattedCoverage = [];
        $defenseJson = json_decode(file_get_contents(__DIR__ . '/data/defenseCoverage.json'));

        foreach ($defenseCoverage as $type => $coverageInfo) {
            // Assuming $coverageInfo might be an array like ['isCovered' => bool, 'cards' => array]
            // If $coverageInfo is just a boolean, adjust accordingly.
            $isCovered = is_array($coverageInfo) ? ($coverageInfo['isCovered'] ?? false) : $coverageInfo;
            $contributingCards = is_array($coverageInfo) ? ($coverageInfo['cards'] ?? []) : []; // Array of card names/objects contributing

            $baseDescription = $defenseJson->descriptions->{$type} ?? 'Descripción no disponible.';
            // Explanation of how it's calculated
            $howItWorks = " Se determina verificando si el mazo incluye cartas efectivas contra este tipo de amenaza (según roles y estadísticas predefinidas).";
            // Show contributing cards if available and covered
            $cardExamples = $isCovered && !empty($contributingCards)
                ? " Cartas en tu mazo que contribuyen: " . htmlspecialchars(implode(', ', $contributingCards)) . "."
                : ($isCovered ? " Se detectaron cartas genéricas que aportan cobertura." : " No se detectaron cartas específicas en tu mazo para esta cobertura.");
            // Meaning of the result
            $meaning = $isCovered ? " Tu mazo parece tener cobertura contra esta amenaza." : " Tu mazo podría ser vulnerable a esta amenaza.";

            $formattedCoverage[$type] = [
                'isCovered' => $isCovered,
                'displayName' => $defenseJson->names->{$type} ?? ucfirst(str_replace('_', ' ', $type)), // Capitalize first letter of each word
                // Combine detailed info for the description/tooltip
                'description' => htmlspecialchars($baseDescription . $howItWorks . $meaning . $cardExamples)
            ];
        }
        return $formattedCoverage; // Return only the formatted types
    }

    /**
     * Analiza las debilidades del mazo basándose en puntajes y cobertura defensiva.
     * Delegado al WeaknessAnalyzer especializado.
     *
     * @return array Un array asociativo con las debilidades identificadas y sugerencias.
     */
    public function getDeckWeaknesses(): array
    {
        $weaknessAnalyzer = new WeaknessAnalyzer(
            $this->constants,
            [$this, 'getBattleRolesScores']
        );

        return $weaknessAnalyzer->analyze($this->Deck);
    }

    /**
     * Identifica el arquetipo del mazo actual basado en definiciones predefinidas.
     * Delegado al ArchetypeAnalyzer especializado.
     *
     * @return array Un array asociativo con el arquetipo identificado y detalles.
     * @throws \Exception Si el archivo de arquetipos no existe o no se puede decodificar.
     */
    public function identifyArchetype(): array
    {
        $archetypeAnalyzer = new ArchetypeAnalyzer(
            [$this, 'getAverageElixirCost'],
            [$this, 'getShortCycle']
        );

        return $archetypeAnalyzer->analyze($this->Deck);
    }

}
