<?php

declare(strict_types=1);

namespace tools\deckanalyzer\analyzers;

use tools\deckanalyzer\AnalyzerInterface;

/**
 * Analizador de Debilidades - Identifica las debilidades del mazo y proporciona sugerencias.
 *
 * Este analizador se encarga de:
 * - Evaluar cobertura defensiva contra diferentes tipos de amenazas
 * - Identificar debilidades en roles de batalla
 * - Proporcionar sugerencias específicas para mejorar el mazo
 * - Analizar tipos de defensa faltantes
 */
class WeaknessAnalyzer implements AnalyzerInterface
{
    /** @var object El mazo que se está analizando */
    private object $deck;

    /** @var object Constantes utilizadas para los cálculos */
    private object $constants;

    /** @var callable Función para obtener puntuaciones de roles de batalla */
    private $getBattleRolesScoresCallback;

    public function __construct(object $constants, callable $getBattleRolesScoresCallback)
    {
        $this->constants = $constants;
        $this->getBattleRolesScoresCallback = $getBattleRolesScoresCallback;
    }

    public function analyze(object $deck): array
    {
        $this->deck = $deck;
        return $this->getDeckWeaknesses();
    }

    /**
     * Analiza las debilidades del mazo basándose en puntajes y cobertura defensiva.
     *
     * Evalúa los puntajes totales de Sinergia, Versatilidad, Ataque y Defensa contra umbrales bajos,
     * y verifica la cobertura de tipos de defensa para identificar vulnerabilidades.
     *
     * @return array Un array asociativo con la clave 'weaknesses',
     *               cuyo valor es otro array asociativo donde las claves son los nombres
     *               de las debilidades y los valores son las sugerencias correspondientes.
     */
    public function getDeckWeaknesses(): array
    {
        $suggestions = [];
        // Umbral para considerar una puntuación como debilidad. Podría moverse a Constants.json.
        $weaknessThreshold = $this->constants->WEAKNESS_THRESHOLD ?? 35; // Increased threshold slightly

        // Obtener puntajes específicos y cobertura
        $attackScores = call_user_func($this->getBattleRolesScoresCallback, 'attack');
        $defenseScores = call_user_func($this->getBattleRolesScoresCallback, 'defense');
        $defenseCoverage = $this->getDefenseTypesCoverage();

        // Mapa de sugerencias dinámicas
        $suggestionMap = [
            // Debilidades de Roles de Batalla (Ataque)
            'attack_low_dps' => sprintf('El DPS promedio de ataque es bajo (%d). Considera cartas ofensivas con más daño como Mini P.E.K.K.A., Leñador, Príncipe o unidades aéreas fuertes.', $attackScores['scoreDps']),
            'attack_low_hitpoints' => sprintf('Las tropas de ataque tienen pocos puntos de vida promedio (%d). Añade un tanque (Gigante, Golem, Sabueso) o semi-tanque (Valquiria, Caballero) para protegerlas.', $attackScores['scoreHitpoints']),
            'attack_low_range' => sprintf('El alcance promedio de ataque es bajo (%d). Incluye cartas de mayor alcance como Mosquetera, Arquero Mágico o Princesa para apoyar desde atrás.', $attackScores['scoreRange']),
            // Debilidades de Roles de Batalla (Defensa)
            'defense_low_dps' => sprintf('El DPS promedio de defensa es bajo (%d). Añade cartas defensivas con buen daño como Cazador, Mini P.E.K.K.A., Torre Infernal o unidades con daño de área.', $defenseScores['scoreDps']),
            'defense_low_hitpoints' => sprintf('Las cartas defensivas tienen pocos puntos de vida promedio (%d). Usa estructuras más resistentes (Tesla, Torre Infernal) o tropas robustas como Valquiria, Megaesbirro o Caballero.', $defenseScores['scoreHitpoints']),
            'defense_low_range' => sprintf('El alcance promedio de defensa es bajo (%d). Considera cartas defensivas de mayor alcance como Torre Tesla, Mago de Hielo, Arquero Mágico o Mosquetera.', $defenseScores['scoreRange']),
            // Debilidades de Cobertura Defensiva
            'coverage_missing_anti_air' => 'Falta defensa anti-aérea fiable. Incluye cartas como Esbirros, Megaesbirro, Mosquetera, Mago Eléctrico, Dragón Infernal o estructuras que ataquen aire.',
            'coverage_missing_anti_tank' => 'Vulnerable a tanques. Añade cartas con alto DPS único (Cazador, Mini P.E.K.K.A., P.E.K.K.A), daño incremental (Torre Infernal, Dragón Infernal) o enjambres (Ejército Esqueletos, Pandilla).',
            'coverage_missing_splash_ground' => 'Débil contra unidades terrestres agrupadas (Ejército, Barril Duendes). Usa cartas con daño de área terrestre como Valquiria, Bombardero, Mago, Tronco, Flechas o Bola de Fuego.',
            'coverage_missing_splash_air' => 'Débil contra unidades aéreas agrupadas (Esbirros, Murciélagos). Considera cartas como Dragón Bebé, Mago, Bruja, Flechas, Descarga, Bola de Nieve o Tornado.',
            'coverage_missing_swarm_defense' => 'Vulnerable a enjambres (terrestres o aéreos). Incluye cartas con daño de área rápido (Flechas, Descarga, Tronco, Bola Nieve), unidades de salpicadura (Valquiria, Mago, Bombardero) o multi-unidades rápidas.',
            'coverage_missing_building_defense' => 'Falta una estructura defensiva para distraer unidades que atacan edificios (Montapuercos, Gigante). Añade edificios como Torre Tesla, Torre Infernal, Cañón o Lápida.',
            'coverage_missing_direct_damage_spell' => 'Sin hechizo de daño directo a torres para rematar partidas. Considera Bola de Fuego, Veneno, Rayo o Cohete.',
            'coverage_missing_control' => 'Falta control para redirigir, ralentizar o reiniciar tropas enemigas. Incluye cartas como Tronco, Descarga, Bola Nieve, Tornado, Hielo, Espíritu de Hielo/Eléctrico o Mago de Hielo/Eléctrico.'
        ];

        // Evaluar puntajes específicos de roles de ataque
        if ($attackScores['scoreDps'] < $weaknessThreshold) {
            $suggestions['Bajo DPS de Ataque'] = $suggestionMap['attack_low_dps'];
        }
        if ($attackScores['scoreHitpoints'] < $weaknessThreshold) {
            $suggestions['Bajos PV de Ataque'] = $suggestionMap['attack_low_hitpoints'];
        }
        if ($attackScores['scoreRange'] < $weaknessThreshold) {
            $suggestions['Bajo Rango de Ataque'] = $suggestionMap['attack_low_range'];
        }

        // Evaluar puntajes específicos de roles de defensa
        if ($defenseScores['scoreDps'] < $weaknessThreshold) {
            $suggestions['Bajo DPS de Defensa'] = $suggestionMap['defense_low_dps'];
        }
        if ($defenseScores['scoreHitpoints'] < $weaknessThreshold) {
            $suggestions['Bajos PV de Defensa'] = $suggestionMap['defense_low_hitpoints'];
        }
        if ($defenseScores['scoreRange'] < $weaknessThreshold) {
            $suggestions['Bajo Rango de Defensa'] = $suggestionMap['defense_low_range'];
        }

        // Evaluar tipos de cobertura defensiva
        if (empty($defenseCoverage['anti_air']['isCovered'])) {
            $suggestions['Falta Cobertura Anti-Aérea'] = $suggestionMap['coverage_missing_anti_air'];
        }
        if (empty($defenseCoverage['anti_tank']['isCovered'])) {
            $suggestions['Falta Cobertura Anti-Tanque'] = $suggestionMap['coverage_missing_anti_tank'];
        }
        if (empty($defenseCoverage['splash_ground']['isCovered'])) {
            $suggestions['Falta Cobertura Terrestre de Área'] = $suggestionMap['coverage_missing_splash_ground'];
        }
        if (empty($defenseCoverage['splash_air']['isCovered'])) {
            $suggestions['Falta Cobertura Aérea de Área'] = $suggestionMap['coverage_missing_splash_air'];
        }
        if (empty($defenseCoverage['swarm_defense']['isCovered'])) {
            $suggestions['Falta Defensa Contra Enjambres'] = $suggestionMap['coverage_missing_swarm_defense'];
        }
        if (empty($defenseCoverage['building_defense']['isCovered'])) {
            $suggestions['Falta Edificio Defensivo'] = $suggestionMap['coverage_missing_building_defense'];
        }
        if (empty($defenseCoverage['direct_damage_spell']['isCovered'])) {
            $suggestions['Falta Hechizo de Daño Directo'] = $suggestionMap['coverage_missing_direct_damage_spell'];
        }
        if (empty($defenseCoverage['control']['isCovered'])) {
            $suggestions['Falta Control'] = $suggestionMap['coverage_missing_control'];
        }

        return [
            "description" => "Las debilidades identifican áreas donde tu mazo podría tener problemas o carencias significativas. Se detectan analizando factores como la falta de cobertura defensiva contra ciertos tipos de ataque (ej. aéreo pesado, enjambres terrestres), un coste de elixir muy alto o bajo que dificulta la gestión, baja sinergia entre cartas clave, o la ausencia de una condición de victoria clara y fiable. Las sugerencias proporcionadas buscan mitigar estas debilidades, proponiendo cambios de cartas específicos o ajustes generales en la estrategia para mejorar el rendimiento y la consistencia del mazo.",
            "suggestions" => $suggestions
        ];
    }

    /**
     * Analiza el mazo para determinar la cobertura de diferentes tipos de defensa.
     *
     * Evalúa las cartas del mazo para identificar si proporcionan defensa contra
     * amenazas aéreas, tanques, enjambres, etc., así como capacidades de control
     * y daño directo mediante hechizos.
     *
     * @return array Un array asociativo donde las claves son los tipos de defensa
     *               (ej. 'anti_air', 'anti_tank') y los valores son arrays con
     *               'isCovered' (boolean) y 'cards' (array de nombres de cartas).
     * @throws \Exception Si ocurre un error al acceder a las propiedades de las cartas o decodificar JSON.
     */
    public function getDefenseTypesCoverage(): array
    {
        $defenseCoverage = [
            'anti_air' => ['isCovered' => false, 'cards' => []],
            'anti_tank' => ['isCovered' => false, 'cards' => []], // Alto daño único o incremental
            'splash_ground' => ['isCovered' => false, 'cards' => []], // Daño de área terrestre
            'splash_air' => ['isCovered' => false, 'cards' => []], // Daño de área aéreo
            'swarm_defense' => ['isCovered' => false, 'cards' => []], // Defensa contra enjambres (multi-unidad, splash, rápido)
            'building_defense' => ['isCovered' => false, 'cards' => []], // Estructuras defensivas
            'direct_damage_spell' => ['isCovered' => false, 'cards' => []], // Hechizos de daño directo a torre
            'control' => ['isCovered' => false, 'cards' => []], // Aturdir, ralentizar, empujar, congelar, etc.
        ];

        // Umbral de DPS para considerar anti-tanque (ajustable)
        $antiTankDpsThreshold = $this->constants->ANTI_TANK_DPS_THRESHOLD ?? 300; // Example, adjust as needed
        $antiTankDamageThreshold = $this->constants->ANTI_TANK_DAMAGE_THRESHOLD ?? 500; // Example for high single hit

        // Atributos de control
        $controlAttributes = $this->constants->CONTROL_ATTRIBUTES_LIST ?? ['stun', 'slow', 'knockback', 'freeze', 'pull', 'charge', 'dash', 'jump']; // Include charge/dash/jump which can interrupt/distract

        foreach (array_merge($this->deck->Cards, $this->deck->CardsEvo) as $card) {
            $specialKeys = $card->special ? array_keys(json_decode(json_encode($card->special), true)) : [];
            $specialEvoKeys = ($card->evolution && $card->specialEvo) ? array_keys(json_decode(json_encode($card->specialEvo), true)) : [];
            $allSpecialKeys = array_unique(array_merge($specialKeys, $specialEvoKeys));
            $cardName = $card->name; // For adding to the list

            // Anti-Aéreo
            if (in_array('aer', $card->Attack)) {
                $defenseCoverage['anti_air']['isCovered'] = true;
                $defenseCoverage['anti_air']['cards'][] = $cardName;
            }

            // Anti-Tanque (Mejorado)
            $isAntiTank = false;
            if ($card->type != 'spell') {
                if (
                    ($card->TypeAttack == 'unique' && ($card->dps ?? 0) >= $antiTankDpsThreshold) || // Alto DPS único
                    (($card->damage ?? 0) >= $antiTankDamageThreshold) || // Alto daño por golpe
                    (in_array('dpsIncrement', $allSpecialKeys)) || // Daño incremental
                    ($card->units > 3 && ($card->dps ?? 0) * $card->units >= $antiTankDpsThreshold * 1.5) // Swarm with high total DPS (e.g., Skarmy, Goblins)
                ) {
                    $isAntiTank = true;
                }
            } elseif ($cardName == 'Rocket' || $cardName == 'Lightning') { // Hechizos de alto daño
                $isAntiTank = true;
            }
            if ($isAntiTank) {
                $defenseCoverage['anti_tank']['isCovered'] = true;
                $defenseCoverage['anti_tank']['cards'][] = $cardName;
            }

            // Salpicadura Terrestre
            if ($card->TypeAttack == 'splash' && in_array('ter', $card->Attack)) {
                $defenseCoverage['splash_ground']['isCovered'] = true;
                $defenseCoverage['splash_ground']['cards'][] = $cardName;
            }

            // Salpicadura Aérea
            if ($card->TypeAttack == 'splash' && in_array('aer', $card->Attack)) {
                $defenseCoverage['splash_air']['isCovered'] = true;
                $defenseCoverage['splash_air']['cards'][] = $cardName;
            }

            // Continue with more coverage types in next chunk...
        }

        // Remove duplicates from card lists
        foreach ($defenseCoverage as &$coverageInfo) {
            if (isset($coverageInfo['cards'])) {
                $coverageInfo['cards'] = array_values(array_unique($coverageInfo['cards']));
            }
        }
        unset($coverageInfo);

        return $defenseCoverage;
    }
}
