## [0.1.2](https://github.com/ClashStrategic/deckanalyzer/compare/v0.1.1...v0.1.2) (2025-07-16)


### Bug Fixes

* **analyzer:** align target checks with card stats ([23e70de](https://github.com/ClashStrategic/deckanalyzer/commit/23e70de1cd51c799b732e2afd7805553a66e4dd1))
* **analyzer:** update target type checks to use card stats properties ([a7594c9](https://github.com/ClashStrategic/deckanalyzer/commit/a7594c914500216329ef5e84ea2d0dc75c8bb9db))
* **card-type:** unify card type string casing ([f7d020c](https://github.com/ClashStrategic/deckanalyzer/commit/f7d020cad9844e73702542786ea01bbb4548705f))

## [0.1.1](https://github.com/ClashStrategic/deckanalyzer/compare/v0.1.0...v0.1.1) (2025-06-03)


### Bug Fixes

* **DeckAnalyzer:** validate Deck instance properties in constructor ([7a1cf5a](https://github.com/ClashStrategic/deckanalyzer/commit/7a1cf5af1160eafd48af221be942fc1d3a895bad))
* **Dependency:** Remove the dependency on Card by using $allCardsInstanceLevel11 instead to get the data for a specific card ([66ae301](https://github.com/ClashStrategic/deckanalyzer/commit/66ae301362d1982215555bbaff25e203ad81be51))
* **Dependency:** Removes dependency on Config class and PAHT_ROOT ([1b08cfb](https://github.com/ClashStrategic/deckanalyzer/commit/1b08cfbb56317cd799da0aa5b727d271c79d3662))
